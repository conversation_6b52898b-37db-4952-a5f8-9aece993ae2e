[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "aircursor-assistant"
version = "1.2.0"
description = "AI-powered hand tracking cursor control with voice commands"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "AirCursor Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "AirCursor Team", email = "<EMAIL>"}
]
keywords = ["hand-tracking", "voice-control", "ai", "cursor", "accessibility"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Graphics :: Capture",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: System :: Hardware :: Hardware Drivers",
]
requires-python = ">=3.12"
dependencies = [
    "opencv-python>=4.8.0",
    "mediapipe>=0.10.0",
    "pyautogui>=0.9.54",
    "speech-recognition>=3.10.0",
    "gtts>=2.4.0",
    "playsound3>=1.3.0",
    "pycaw>=20220416",
    "google-generativeai>=0.3.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0",
    "requests>=2.31.0",
    "selenium>=4.15.0",
    "pytesseract>=0.3.10",
    "comtypes>=1.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "bandit>=1.7.5",
    "safety>=2.3.0",
    "psutil>=5.9.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",
    "psutil>=5.9.0",
]
performance = [
    "psutil>=5.9.0",
    "memory-profiler>=0.61.0",
]

[project.urls]
Homepage = "https://github.com/aircursor/aircursor-assistant"
Documentation = "https://github.com/aircursor/aircursor-assistant/blob/main/README.md"
Repository = "https://github.com/aircursor/aircursor-assistant"
"Bug Tracker" = "https://github.com/aircursor/aircursor-assistant/issues"
Changelog = "https://github.com/aircursor/aircursor-assistant/blob/main/CHANGELOG.md"

[project.scripts]
aircursor = "main:main"

# Black konfigurácia
[tool.black]
line-length = 100
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# isort konfigurácia
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["air_cursor", "voice_commands", "deepseek_integration", "config_manager", "screen_reader"]
known_third_party = ["cv2", "mediapipe", "pyautogui", "speech_recognition", "gtts", "playsound3", "pycaw", "google", "PIL", "numpy", "requests", "selenium", "pytesseract", "comtypes"]

# MyPy konfigurácia
[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "mediapipe.*",
    "pyautogui.*",
    "speech_recognition.*",
    "gtts.*",
    "playsound3.*",
    "pycaw.*",
    "google.generativeai.*",
    "PIL.*",
    "numpy.*",
    "requests.*",
    "selenium.*",
    "pytesseract.*",
    "comtypes.*",
]
ignore_missing_imports = true

# Pytest konfigurácia
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "api: marks tests that require API access",
    "hardware: marks tests that require hardware (camera, microphone)",
    "gui: marks tests that require GUI",
    "network: marks tests that require network access",
    "performance: marks tests as performance tests",
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore:.*tensorflow.*:UserWarning",
    "ignore:.*mediapipe.*:UserWarning",
]

# Coverage konfigurácia
[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/build/*",
    "*/dist/*",
    "setup.py",
    "install.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

# Bandit konfigurácia
[tool.bandit]
exclude_dirs = ["tests", "venv", "build", "dist"]
skips = ["B101", "B601"]  # B101: assert_used, B601: paramiko_calls

# setuptools konfigurácia
[tool.setuptools]
package-dir = {"" = "."}

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]
exclude = ["tests*", "venv*", "build*", "dist*"]
