[tool:pytest]
# Pytest konfigur<PERSON><PERSON> pre AirCursor Assistant

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output formatting
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --color=yes
    --durations=10
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80

# Markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests that require API access
    hardware: marks tests that require hardware (camera, microphone)
    gui: marks tests that require GUI
    network: marks tests that require network access

# Warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*tensorflow.*:UserWarning
    ignore:.*mediapipe.*:UserWarning

# Minimum version
minversion = 7.0

# Test timeout (seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment for parallel execution with pytest-xdist
