#!/usr/bin/env python3
"""
Test pre Fázu 5: <PERSON><PERSON><PERSON>č<PERSON><PERSON> funkcie

Overuje implementáciu <PERSON>, v<PERSON><PERSON><PERSON><PERSON><PERSON>, pluginov a webového rozhrania.
"""

import os
import sys
import time
from pathlib import Path

def print_colored(text, color_code):
    """Vytlačí farebný text."""
    print(f"\033[{color_code}m{text}\033[0m")

def print_success(text):
    print_colored(f"✅ {text}", "92")

def print_error(text):
    print_colored(f"❌ {text}", "91")

def print_info(text):
    print_colored(f"ℹ️  {text}", "94")

def print_header(text):
    print_colored(f"\n🚀 {text}", "96;1")
    print_colored("=" * 50, "96")

def test_file_exists(file_path, description):
    """Testuje existenciu súboru."""
    if Path(file_path).exists():
        print_success(f"{description}: {file_path}")
        return True
    else:
        print_error(f"{description}: {file_path} - CHÝBA")
        return False

def test_imports():
    """Testuje importy nových modulov."""
    print_header("Test importov Fázy 5")
    
    modules = [
        ("ui_animations", "UI Animations"),
        ("custom_voice_commands", "Custom Voice Commands"),
        ("plugin_system", "Plugin System"),
        ("web_interface", "Web Interface (voliteľný)"),
    ]
    
    success_count = 0
    for module_name, description in modules:
        try:
            __import__(module_name)
            print_success(f"{description}: {module_name}")
            success_count += 1
        except ImportError as e:
            if module_name == "web_interface":
                print_info(f"{description}: {module_name} - Flask nie je nainštalovaný")
            else:
                print_error(f"{description}: {module_name} - {e}")
        except Exception as e:
            print_error(f"{description}: {module_name} - Neočakávaná chyba: {e}")
    
    return success_count

def test_animation_system():
    """Testuje animačný systém."""
    print_header("Test animačného systému")
    
    try:
        from ui_animations import AnimationManager, animate_widget, get_animation_manager
        
        # Test AnimationManager
        manager = AnimationManager()
        print_success("AnimationManager vytvorený")
        
        # Test globálneho managera
        global_manager = get_animation_manager()
        print_success("Globálny animation manager dostupný")
        
        # Test, že má potrebné metódy
        required_methods = ['animate_fade', 'animate_slide', 'animate_scale', 'animate_color', 'animate_pulse']
        for method in required_methods:
            if hasattr(manager, method):
                print_success(f"Metóda {method} dostupná")
            else:
                print_error(f"Metóda {method} chýba")
                return False
        
        print_success("Animačný systém je kompletný")
        return True
        
    except Exception as e:
        print_error(f"Chyba v animačnom systéme: {e}")
        return False

def test_custom_commands():
    """Testuje systém vlastných príkazov."""
    print_header("Test vlastných príkazov")
    
    try:
        from custom_voice_commands import CustomCommand, CustomVoiceCommandManager, create_sample_commands
        
        # Test CustomCommand
        cmd = CustomCommand(
            name="test_command",
            triggers=["test"],
            action_type="keyboard",
            action_data={"keys": ["ctrl", "s"]},
            description="Test príkaz"
        )
        print_success("CustomCommand vytvorený")
        
        # Test serializácie
        cmd_dict = cmd.to_dict()
        cmd_restored = CustomCommand.from_dict(cmd_dict)
        print_success("Serializácia/deserializácia funguje")
        
        # Test managera
        manager = CustomVoiceCommandManager("test_commands.json")
        print_success("CustomVoiceCommandManager vytvorený")
        
        # Test ukážkových príkazov
        sample_commands = create_sample_commands()
        print_success(f"Vytvorených {len(sample_commands)} ukážkových príkazov")
        
        # Test pridania príkazu
        success = manager.add_command(sample_commands[0])
        if success:
            print_success("Pridanie príkazu funguje")
        
        # Test vyhľadávania
        found = manager.find_command_by_trigger("ulož súbor")
        if found:
            print_success("Vyhľadávanie príkazov funguje")
        
        # Cleanup
        if Path("test_commands.json").exists():
            os.remove("test_commands.json")
        
        print_success("Systém vlastných príkazov je kompletný")
        return True
        
    except Exception as e:
        print_error(f"Chyba v systéme vlastných príkazov: {e}")
        return False

def test_plugin_system():
    """Testuje plugin systém."""
    print_header("Test plugin systému")
    
    try:
        from plugin_system import PluginInterface, PluginManager, PluginMetadata
        
        # Test PluginManager
        manager = PluginManager("test_plugins")
        print_success("PluginManager vytvorený")
        
        # Test objavovania pluginov
        manager.discover_plugins()
        print_success("Objavovanie pluginov funguje")
        
        # Test weather pluginu ak existuje
        if Path("plugins/weather_plugin.py").exists():
            plugins = manager.list_plugins()
            weather_plugin = None
            for plugin in plugins:
                if plugin.name == "weather_plugin":
                    weather_plugin = plugin
                    break
            
            if weather_plugin:
                print_success("Weather plugin objavený")
                
                # Test načítania
                success = manager.load_plugin("weather_plugin", {"config": {"weather_api_key": "demo"}})
                if success:
                    print_success("Weather plugin načítaný")
                else:
                    print_info("Weather plugin sa nepodarilo načítať (normálne pre demo)")
            else:
                print_info("Weather plugin nebol objavený")
        
        # Cleanup
        if Path("test_plugins").exists():
            import shutil
            shutil.rmtree("test_plugins")
        
        print_success("Plugin systém je kompletný")
        return True
        
    except Exception as e:
        print_error(f"Chyba v plugin systéme: {e}")
        return False

def test_web_interface():
    """Testuje webové rozhranie."""
    print_header("Test webového rozhrania")
    
    try:
        from web_interface import WebInterface
        
        # Test vytvorenia
        web = WebInterface(port=8081)  # Iný port pre test
        print_success("WebInterface vytvorený")
        
        # Test, že má potrebné metódy
        required_methods = ['start', 'stop']
        for method in required_methods:
            if hasattr(web, method):
                print_success(f"Metóda {method} dostupná")
            else:
                print_error(f"Metóda {method} chýba")
                return False
        
        print_success("Webové rozhranie je kompletné")
        return True
        
    except ImportError:
        print_info("Webové rozhranie: Flask nie je nainštalovaný")
        return True  # Nie je chyba, iba chýba závislosť
    except Exception as e:
        print_error(f"Chyba vo webovom rozhraní: {e}")
        return False

def test_main_integration():
    """Testuje integráciu s main.py."""
    print_header("Test integrácie s main.py")
    
    try:
        # Test importu main.py s novými modulmi
        import main
        print_success("main.py sa importuje s novými modulmi")
        
        # Test, že má nové atribúty
        if hasattr(main, 'AnimationManager'):
            print_success("AnimationManager importovaný v main.py")
        
        if hasattr(main, 'CustomVoiceCommandManager'):
            print_success("CustomVoiceCommandManager importovaný v main.py")
        
        if hasattr(main, 'PluginManager'):
            print_success("PluginManager importovaný v main.py")
        
        print_success("Integrácia s main.py je úspešná")
        return True
        
    except Exception as e:
        print_error(f"Chyba pri integrácii s main.py: {e}")
        return False

def main():
    """Hlavná funkcia."""
    print_header("Fáza 5 - Test pokročilých funkcií")
    print_info("Overujem implementáciu animácií, vlastných príkazov, pluginov a webového rozhrania...")
    
    # Test súborov
    print_header("Kontrola súborov")
    
    required_files = [
        ("ui_animations.py", "UI Animations modul"),
        ("custom_voice_commands.py", "Custom Voice Commands modul"),
        ("plugin_system.py", "Plugin System modul"),
        ("web_interface.py", "Web Interface modul"),
        ("plugins/weather_plugin.py", "Weather Plugin"),
        ("PHASE5_ADVANCED_FEATURES.md", "Fáza 5 dokumentácia")
    ]
    
    files_ok = 0
    for file_path, description in required_files:
        if test_file_exists(file_path, description):
            files_ok += 1
    
    # Test importov
    imports_ok = test_imports()
    
    # Test jednotlivých systémov
    animation_ok = test_animation_system()
    commands_ok = test_custom_commands()
    plugins_ok = test_plugin_system()
    web_ok = test_web_interface()
    main_ok = test_main_integration()
    
    # Súhrn
    print_header("Súhrn testov Fázy 5")
    
    total_tests = 6
    passed_tests = sum([
        files_ok == len(required_files),
        animation_ok,
        commands_ok,
        plugins_ok,
        web_ok,
        main_ok
    ])
    
    print_info(f"Súbory: {files_ok}/{len(required_files)}")
    print_info(f"Importy: {imports_ok}/4 modulov")
    print_info(f"Animácie: {'✅' if animation_ok else '❌'}")
    print_info(f"Vlastné príkazy: {'✅' if commands_ok else '❌'}")
    print_info(f"Plugin systém: {'✅' if plugins_ok else '❌'}")
    print_info(f"Webové rozhranie: {'✅' if web_ok else '❌'}")
    print_info(f"Main integrácia: {'✅' if main_ok else '❌'}")
    
    success_rate = (passed_tests / total_tests) * 100
    
    if success_rate >= 80:
        print_success(f"\n🎉 Fáza 5 je úspešne implementovaná! ({success_rate:.0f}%)")
        print_info("Nové funkcie:")
        print_info("  🎨 Animácie a prechody")
        print_info("  🎤 Vlastné hlasové príkazy")
        print_info("  🔌 Plugin systém")
        print_info("  🌐 Webové rozhranie")
        print_info("\nMôžete spustiť: python main.py")
        return 0
    else:
        print_error(f"\n💥 Niektoré testy zlyhali ({success_rate:.0f}%)")
        print_info("Skontrolujte chyby vyššie a opravte ich")
        return 1

if __name__ == "__main__":
    sys.exit(main())
