#!/usr/bin/env python3
"""
Kontrola aktivácie OpenWeatherMap API kľúča
"""

import time
import requests
from datetime import datetime

def check_api_key():
    """Skontroluje API kľúč."""
    API_KEY = "f98c81adc492ff4ea3871b364025957b"
    url = "http://api.openweathermap.org/data/2.5/weather"
    params = {
        'q': 'Bratislava',
        'appid': API_KEY,
        'units': 'metric',
        'lang': 'sk'
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            city = data['name']
            temp = data['main']['temp']
            description = data['weather'][0]['description']
            
            print(f"✅ {datetime.now().strftime('%H:%M:%S')} - API kľúč je AKTÍVNY!")
            print(f"🌤️ {city}: {temp}°C, {description}")
            return True
            
        elif response.status_code == 401:
            print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - API kľúč sa ešte aktivuje...")
            return False
            
        else:
            print(f"❌ {datetime.now().strftime('%H:%M:%S')} - API chyba: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ {datetime.now().strftime('%H:%M:%S')} - Chyba: {e}")
        return False

def main():
    """Hlavná funkcia."""
    print("🔄 Kontrola aktivácie API kľúča")
    print("=" * 40)
    print("API kľúč: f98c81adc492ff4ea3871b364025957b")
    print("Kontrolujem každých 30 sekúnd...")
    print("Stlačte Ctrl+C pre ukončenie")
    print()
    
    attempt = 1
    max_attempts = 120  # 60 minút (120 x 30 sekúnd)
    
    while attempt <= max_attempts:
        print(f"🔍 Pokus {attempt}/{max_attempts}")
        
        if check_api_key():
            print("\n🎉 API kľúč je aktivovaný!")
            print("✅ Môžete teraz spustiť aplikáciu s real weather dátami")
            print("🚀 Spustite: python main.py")
            break
        
        if attempt < max_attempts:
            print("   Čakám 30 sekúnd...\n")
            time.sleep(30)
        
        attempt += 1
    
    else:
        print("\n⏰ API kľúč sa stále neaktivoval")
        print("💡 Možné riešenia:")
        print("   1. Skontrolujte email a potvrďte účet")
        print("   2. Skontrolujte API kľúč na openweathermap.org")
        print("   3. Skúste neskôr")
        print("   4. Demo režim stále funguje")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Kontrola ukončená používateľom")
        print("💡 Môžete skúsiť neskôr alebo použiť demo režim")
