"""
Pytest konfigur<PERSON>cia a fixtures pre AirCursor Assistant testy.
"""

import pytest
import os
import sys
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import json

# Pridanie project root do Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

@pytest.fixture(scope="session")
def project_root_path():
    """Vr<PERSON>ti cestu k project root."""
    return project_root

@pytest.fixture(scope="session")
def test_data_dir():
    """Vr<PERSON>ti cestu k test data directory."""
    data_dir = project_root / "tests" / "data"
    data_dir.mkdir(exist_ok=True)
    return data_dir

@pytest.fixture
def temp_dir():
    """Vytvorí dočasný directory pre test."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path, ignore_errors=True)

@pytest.fixture
def mock_api_key():
    """Mock API kľúč pre testovanie."""
    return "test_gemini_api_key_12345"

@pytest.fixture
def mock_env_with_api_key(mock_api_key):
    """Mock environment s API kľúčom."""
    with patch.dict(os.environ, {'GEMINI_API_KEY': mock_api_key}):
        yield mock_api_key

@pytest.fixture
def mock_env_without_api_key():
    """Mock environment bez API kľúča."""
    env_copy = os.environ.copy()
    if 'GEMINI_API_KEY' in env_copy:
        del env_copy['GEMINI_API_KEY']
    with patch.dict(os.environ, env_copy, clear=True):
        yield

@pytest.fixture
def mock_config():
    """Mock konfigurácia pre testovanie."""
    return {
        "hand_tracking": {
            "max_hands": 1,
            "detection_confidence": 0.7,
            "tracking_confidence": 0.5
        },
        "cursor": {
            "failsafe": True,
            "smoothing": 0.5,
            "sensitivity_x": 1.3,
            "sensitivity_y": 1.3
        },
        "camera": {
            "device_id": 0,
            "width": 640,
            "height": 480
        },
        "gemini": {
            "model": "gemini-2.0-flash",
            "timeout": 30,
            "max_response_length": 300,
            "context_memory": True,
            "max_history_length": 5
        },
        "voice": {
            "commands": {
                "click": ["klikni", "klik"],
                "right_click": ["pravý klik", "pravé kliknutie"],
                "ask": ["spýtaj sa", "otázka", "povedz mi"]
            }
        }
    }

@pytest.fixture
def mock_config_manager(mock_config):
    """Mock ConfigManager pre testovanie."""
    with patch('config_manager.ConfigManager') as mock_cm:
        mock_instance = Mock()
        
        def mock_get(*args, **kwargs):
            """Mock implementácia get metódy."""
            config = mock_config
            for key in args:
                if isinstance(config, dict) and key in config:
                    config = config[key]
                else:
                    return kwargs.get('default', None)
            return config
        
        mock_instance.get.side_effect = mock_get
        mock_cm.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def mock_cv2():
    """Mock OpenCV pre testovanie."""
    with patch('cv2.VideoCapture') as mock_cap:
        mock_instance = Mock()
        mock_instance.read.return_value = (True, Mock())  # (success, frame)
        mock_instance.isOpened.return_value = True
        mock_instance.get.return_value = 640  # width/height
        mock_cap.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def mock_mediapipe():
    """Mock MediaPipe pre testovanie."""
    with patch('mediapipe.solutions.hands') as mock_hands:
        mock_hands_instance = Mock()
        mock_hands_instance.process.return_value = Mock(multi_hand_landmarks=None)
        mock_hands.Hands.return_value = mock_hands_instance
        yield mock_hands_instance

@pytest.fixture
def mock_gemini_api():
    """Mock Gemini API pre testovanie."""
    with patch('google.generativeai.configure'):
        with patch('google.generativeai.GenerativeModel') as mock_model:
            mock_instance = Mock()
            mock_response = Mock()
            mock_response.text = "Test response"
            mock_instance.generate_content.return_value = mock_response
            mock_model.return_value = mock_instance
            yield mock_instance

@pytest.fixture
def mock_tkinter():
    """Mock Tkinter pre testovanie GUI."""
    with patch('tkinter.Tk') as mock_tk:
        mock_root = Mock()
        mock_tk.return_value = mock_root
        yield mock_root

@pytest.fixture
def mock_audio():
    """Mock audio komponenty (TTS, STT)."""
    with patch('gtts.gTTS') as mock_gtts:
        with patch('playsound3.playsound') as mock_playsound:
            with patch('speech_recognition.Recognizer') as mock_recognizer:
                mock_tts = Mock()
                mock_gtts.return_value = mock_tts
                
                mock_rec = Mock()
                mock_recognizer.return_value = mock_rec
                
                yield {
                    'tts': mock_tts,
                    'playsound': mock_playsound,
                    'recognizer': mock_rec
                }

@pytest.fixture
def sample_test_data(test_data_dir):
    """Vytvorí sample test data súbory."""
    # Sample config
    config_file = test_data_dir / "test_config.json"
    with open(config_file, 'w') as f:
        json.dump({
            "test": True,
            "camera": {"device_id": 0}
        }, f)
    
    # Sample calibration
    calibration_file = test_data_dir / "test_calibration.json"
    with open(calibration_file, 'w') as f:
        json.dump({
            "top_left": [0, 0],
            "top_right": [640, 0],
            "bottom_left": [0, 480],
            "bottom_right": [640, 480]
        }, f)
    
    yield {
        'config': config_file,
        'calibration': calibration_file
    }
    
    # Cleanup
    config_file.unlink(missing_ok=True)
    calibration_file.unlink(missing_ok=True)

@pytest.fixture(autouse=True)
def cleanup_temp_files():
    """Automatické vyčistenie dočasných súborov po testoch."""
    yield
    
    # Vyčistenie dočasných súborov
    temp_files = [
        "temp_audio.mp3",
        "test_output.txt",
        "test_config.json"
    ]
    
    for file in temp_files:
        file_path = project_root / file
        if file_path.exists():
            file_path.unlink()

# Pytest hooks
def pytest_configure(config):
    """Pytest konfigurácia."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "api: marks tests that require API access"
    )

def pytest_collection_modifyitems(config, items):
    """Modifikácia test items."""
    # Pridanie slow marker pre testy s dlhým runtime
    for item in items:
        if "integration" in item.keywords or "api" in item.keywords:
            item.add_marker(pytest.mark.slow)
