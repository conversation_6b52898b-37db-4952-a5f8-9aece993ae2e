"""
Test package pre AirCursor Assistant

<PERSON><PERSON><PERSON><PERSON> v<PERSON> unit testy, integračn<PERSON> testy a pomocné nástroje.
"""

import sys
import os
from pathlib import Path

# Pridanie project root do Python path pre testy
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test konfigurácia
TEST_CONFIG = {
    "timeout": 30,
    "mock_api_key": "test_api_key_12345",
    "test_data_dir": project_root / "tests" / "data",
    "temp_dir": project_root / "tests" / "temp"
}

# Vytvorenie test directories ak neexistujú
TEST_CONFIG["test_data_dir"].mkdir(exist_ok=True)
TEST_CONFIG["temp_dir"].mkdir(exist_ok=True)
