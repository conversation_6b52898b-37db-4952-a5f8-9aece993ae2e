#!/usr/bin/env python3
"""
Unit testy pre screen_reader.py modul

Testuje funkcionalitu OCR a webovej automatizácie.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Pridanie project root do Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestScreenReader(unittest.TestCase):
    """Test trieda pre screen_reader modul."""

    def setUp(self):
        """Nastavenie pre každý test."""
        # Mock všetky závislosti
        self.pytesseract_patcher = patch('screen_reader.pytesseract')
        self.mock_pytesseract = self.pytesseract_patcher.start()
        
        self.pil_patcher = patch('screen_reader.Image')
        self.mock_pil = self.pil_patcher.start()
        
        self.pyautogui_patcher = patch('screen_reader.pyautogui')
        self.mock_pyautogui = self.pyautogui_patcher.start()
        
        self.selenium_patcher = patch('screen_reader.webdriver')
        self.mock_selenium = self.selenium_patcher.start()
        
        self.requests_patcher = patch('screen_reader.requests')
        self.mock_requests = self.requests_patcher.start()

    def tearDown(self):
        """Cleanup po testoch."""
        self.pytesseract_patcher.stop()
        self.pil_patcher.stop()
        self.pyautogui_patcher.stop()
        self.selenium_patcher.stop()
        self.requests_patcher.stop()

    def test_screen_capture(self):
        """Test zachytenia obrazovky."""
        # Mock screenshot
        mock_screenshot = Mock()
        self.mock_pyautogui.screenshot.return_value = mock_screenshot
        
        try:
            from screen_reader import capture_screen
            
            result = capture_screen()
            
            self.mock_pyautogui.screenshot.assert_called_once()
            self.assertEqual(result, mock_screenshot)
            
            print("✅ Zachytenie obrazovky funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_ocr_text_extraction(self):
        """Test extrakcie textu pomocou OCR."""
        # Mock OCR výsledok
        self.mock_pytesseract.image_to_string.return_value = "Test text na obrazovke"
        
        try:
            from screen_reader import extract_text_from_image
            
            mock_image = Mock()
            result = extract_text_from_image(mock_image)
            
            self.mock_pytesseract.image_to_string.assert_called_once_with(mock_image)
            self.assertEqual(result, "Test text na obrazovke")
            
            print("✅ OCR extrakcia textu funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_text_search_on_screen(self):
        """Test vyhľadávania textu na obrazovke."""
        # Mock screenshot a OCR
        mock_screenshot = Mock()
        self.mock_pyautogui.screenshot.return_value = mock_screenshot
        self.mock_pytesseract.image_to_string.return_value = "Toto je test text na obrazovke"
        
        try:
            from screen_reader import find_text_on_screen
            
            # Test nájdenia textu
            result = find_text_on_screen("test text")
            self.assertTrue(result)
            
            # Test nenájdenia textu
            result = find_text_on_screen("neexistujúci text")
            self.assertFalse(result)
            
            print("✅ Vyhľadávanie textu na obrazovke funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_web_automation_setup(self):
        """Test nastavenia webovej automatizácie."""
        # Mock webdriver
        mock_driver = Mock()
        self.mock_selenium.Chrome.return_value = mock_driver
        
        try:
            from screen_reader import setup_web_driver
            
            driver = setup_web_driver()
            
            self.mock_selenium.Chrome.assert_called_once()
            self.assertEqual(driver, mock_driver)
            
            print("✅ Nastavenie webovej automatizácie funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_web_element_interaction(self):
        """Test interakcie s webovými elementmi."""
        # Mock webdriver a element
        mock_driver = Mock()
        mock_element = Mock()
        mock_driver.find_element.return_value = mock_element
        
        try:
            from screen_reader import click_web_element
            
            result = click_web_element(mock_driver, "button", "Submit")
            
            mock_driver.find_element.assert_called_once()
            mock_element.click.assert_called_once()
            self.assertTrue(result)
            
            print("✅ Interakcia s webovými elementmi funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_voice_command_processing(self):
        """Test spracovania hlasových príkazov."""
        try:
            from screen_reader import process_voice_command
            
            # Test čítania obrazovky
            with patch('screen_reader.find_text_on_screen', return_value=True):
                success, message = process_voice_command("prečítaj obrazovku")
                self.assertTrue(success)
                self.assertIn("text", message.lower())
            
            # Test nerozpoznaného príkazu
            success, message = process_voice_command("neznámy príkaz")
            self.assertFalse(success)
            
            print("✅ Spracovanie hlasových príkazov funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_error_handling(self):
        """Test error handling."""
        # Test OCR chyby
        self.mock_pytesseract.image_to_string.side_effect = Exception("OCR error")
        
        try:
            from screen_reader import extract_text_from_image
            
            mock_image = Mock()
            result = extract_text_from_image(mock_image)
            
            # Očakávame, že funkcia vráti prázdny string alebo None pri chybe
            self.assertIn(result, [None, "", "Chyba pri čítaní textu"])
            
            print("✅ Error handling funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_image_preprocessing(self):
        """Test predspracovania obrázkov pre OCR."""
        try:
            from screen_reader import preprocess_image_for_ocr
            
            mock_image = Mock()
            mock_processed = Mock()
            
            # Mock PIL operácie
            mock_image.convert.return_value = mock_processed
            
            result = preprocess_image_for_ocr(mock_image)
            
            # Overenie, že sa zavolali preprocessing operácie
            mock_image.convert.assert_called()
            
            print("✅ Predspracovanie obrázkov funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

    def test_coordinate_detection(self):
        """Test detekcie súradníc textu."""
        # Mock OCR s pozičnými dátami
        mock_data = {
            'text': ['', 'Test', 'text', ''],
            'left': [0, 100, 200, 0],
            'top': [0, 50, 50, 0],
            'width': [0, 50, 50, 0],
            'height': [0, 20, 20, 0]
        }
        self.mock_pytesseract.image_to_data.return_value = mock_data
        
        try:
            from screen_reader import find_text_coordinates
            
            mock_image = Mock()
            coords = find_text_coordinates(mock_image, "Test")
            
            self.assertIsNotNone(coords)
            self.mock_pytesseract.image_to_data.assert_called_once()
            
            print("✅ Detekcia súradníc textu funguje správne")
            
        except ImportError:
            print("⚠️  screen_reader modul nie je dostupný")

def run_screen_reader_tests():
    """Spustí testy pre screen_reader modul."""
    print("🧪 Spúšťam testy pre screen_reader.py...")
    print("=" * 50)
    
    # Test importu
    try:
        with patch('screen_reader.pytesseract'), patch('screen_reader.Image'):
            with patch('screen_reader.pyautogui'), patch('screen_reader.webdriver'):
                with patch('screen_reader.requests'):
                    import screen_reader
                    print("✅ Import screen_reader.py - PASSED")
    except Exception as e:
        print(f"❌ Import screen_reader.py - FAILED: {e}")
        return False
    
    print("\n🎉 Všetky základné testy pre screen_reader prešli úspešne!")
    return True

if __name__ == "__main__":
    print("🚀 AirCursor Assistant - Test screen_reader.py")
    print("=" * 50)
    
    # Spustenie základných testov
    basic_success = run_screen_reader_tests()
    
    if basic_success:
        print("\n🧪 Spúšťam unit testy...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("\n❌ Základné testy zlyhali, preskakujem unit testy")
        sys.exit(1)
