"""
Unit testy pre config_manager.py
"""

import pytest
import json
import os
from pathlib import Path
from unittest.mock import patch, mock_open

from config_manager import ConfigManager


class TestConfigManager:
    """Test trieda pre ConfigManager."""
    
    def test_init_with_default_config(self, temp_dir):
        """Test inicializácie s default config súborom."""
        config_file = temp_dir / "config.json"
        test_config = {"test": "value"}
        
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        
        cm = ConfigManager(str(config_file))
        assert cm.config_file == config_file
        assert cm.config == test_config
    
    def test_init_with_missing_config(self, temp_dir):
        """Test inicializácie s chýbajúcim config súborom."""
        config_file = temp_dir / "missing_config.json"
        
        cm = ConfigManager(str(config_file))
        assert cm.config == {}
    
    def test_load_config_valid_json(self, temp_dir):
        """Test načítania platného JSON config súboru."""
        config_file = temp_dir / "valid_config.json"
        test_config = {
            "camera": {"device_id": 0},
            "gemini": {"model": "test-model"}
        }
        
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        
        cm = ConfigManager(str(config_file))
        loaded_config = cm.load_config()
        
        assert loaded_config == test_config
    
    def test_load_config_invalid_json(self, temp_dir):
        """Test načítania neplatného JSON súboru."""
        config_file = temp_dir / "invalid_config.json"
        
        with open(config_file, 'w') as f:
            f.write("invalid json content {")
        
        cm = ConfigManager(str(config_file))
        loaded_config = cm.load_config()
        
        assert loaded_config == {}
    
    def test_get_single_key(self, mock_config, temp_dir):
        """Test get metódy s jedným kľúčom."""
        config_file = temp_dir / "test_config.json"
        
        with open(config_file, 'w') as f:
            json.dump(mock_config, f)
        
        cm = ConfigManager(str(config_file))
        
        # Test existujúceho kľúča
        camera_config = cm.get("camera")
        assert camera_config == mock_config["camera"]
        
        # Test neexistujúceho kľúča
        missing_value = cm.get("missing_key")
        assert missing_value is None
        
        # Test s default hodnotou
        default_value = cm.get("missing_key", default="default")
        assert default_value == "default"
    
    def test_get_nested_keys(self, mock_config, temp_dir):
        """Test get metódy s vnoreným kľúčmi."""
        config_file = temp_dir / "test_config.json"
        
        with open(config_file, 'w') as f:
            json.dump(mock_config, f)
        
        cm = ConfigManager(str(config_file))
        
        # Test vnorených kľúčov
        device_id = cm.get("camera", "device_id")
        assert device_id == 0
        
        # Test hlboko vnorených kľúčov
        click_commands = cm.get("voice", "commands", "click")
        assert click_commands == ["klikni", "klik"]
        
        # Test neexistujúceho vnoreného kľúča
        missing_nested = cm.get("camera", "missing_setting")
        assert missing_nested is None
        
        # Test s default hodnotou
        default_nested = cm.get("camera", "missing_setting", default=42)
        assert default_nested == 42
    
    def test_get_with_non_dict_value(self, temp_dir):
        """Test get metódy keď hodnota nie je dictionary."""
        config_file = temp_dir / "test_config.json"
        test_config = {
            "simple_value": "string",
            "number_value": 42,
            "list_value": [1, 2, 3]
        }
        
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        
        cm = ConfigManager(str(config_file))
        
        # Test prístupu k vnoreným kľúčom v non-dict hodnote
        result = cm.get("simple_value", "nested_key")
        assert result is None
        
        # Test s default hodnotou
        result_with_default = cm.get("simple_value", "nested_key", default="fallback")
        assert result_with_default == "fallback"
    
    @patch.dict(os.environ, {'TEST_VAR': 'test_value'})
    def test_load_env_file_existing(self, temp_dir):
        """Test načítania existujúceho .env súboru."""
        env_file = temp_dir / ".env"
        env_content = """
# Test environment file
TEST_KEY=test_value
ANOTHER_KEY=another_value
QUOTED_KEY="quoted value"
SINGLE_QUOTED='single quoted'
EMPTY_KEY=
# Comment line
SPACED_KEY = spaced value
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        # Zmena working directory pre test
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        try:
            cm = ConfigManager()
            cm._load_env_file()
            
            # Overenie, že premenné boli nastavené
            assert os.environ.get('TEST_KEY') == 'test_value'
            assert os.environ.get('ANOTHER_KEY') == 'another_value'
            assert os.environ.get('QUOTED_KEY') == 'quoted value'
            assert os.environ.get('SINGLE_QUOTED') == 'single quoted'
            assert os.environ.get('SPACED_KEY') == 'spaced value'
            
        finally:
            os.chdir(original_cwd)
    
    def test_load_env_file_missing(self, temp_dir):
        """Test načítania neexistujúceho .env súboru."""
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        try:
            cm = ConfigManager()
            # Nemalo by vyhodiť chybu
            cm._load_env_file()
            
        finally:
            os.chdir(original_cwd)
    
    def test_load_env_file_malformed(self, temp_dir):
        """Test načítania poškodeného .env súboru."""
        env_file = temp_dir / ".env"
        
        with open(env_file, 'w') as f:
            f.write("MALFORMED LINE WITHOUT EQUALS\n")
            f.write("=EMPTY_KEY\n")
            f.write("VALID_KEY=valid_value\n")
        
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        try:
            cm = ConfigManager()
            cm._load_env_file()
            
            # Platná hodnota by mala byť načítaná
            assert os.environ.get('VALID_KEY') == 'valid_value'
            
        finally:
            os.chdir(original_cwd)
    
    def test_env_file_does_not_override_existing(self, temp_dir):
        """Test, že .env súbor neprepíše existujúce environment variables."""
        # Nastavenie existujúcej premennej
        with patch.dict(os.environ, {'EXISTING_VAR': 'original_value'}):
            env_file = temp_dir / ".env"
            
            with open(env_file, 'w') as f:
                f.write("EXISTING_VAR=new_value\n")
                f.write("NEW_VAR=new_value\n")
            
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                cm = ConfigManager()
                cm._load_env_file()
                
                # Existujúca premenná by nemala byť prepísaná
                assert os.environ.get('EXISTING_VAR') == 'original_value'
                
                # Nová premenná by mala byť nastavená
                assert os.environ.get('NEW_VAR') == 'new_value'
                
            finally:
                os.chdir(original_cwd)


@pytest.mark.integration
class TestConfigManagerIntegration:
    """Integračné testy pre ConfigManager."""
    
    def test_real_config_file_loading(self, project_root_path):
        """Test načítania skutočného config.json súboru."""
        config_file = project_root_path / "config.json"
        
        if config_file.exists():
            cm = ConfigManager(str(config_file))
            
            # Overenie základných sekcií
            assert "camera" in cm.config
            assert "gemini" in cm.config
            
            # Overenie špecifických hodnôt
            camera_config = cm.get("camera")
            assert isinstance(camera_config, dict)
            assert "device_id" in camera_config
    
    def test_config_manager_with_real_env_file(self, project_root_path):
        """Test s reálnym .env súborom ak existuje."""
        env_file = project_root_path / ".env"
        
        if env_file.exists():
            original_cwd = os.getcwd()
            os.chdir(project_root_path)
            
            try:
                cm = ConfigManager()
                # Nemalo by vyhodiť chybu
                assert cm is not None
                
            finally:
                os.chdir(original_cwd)
