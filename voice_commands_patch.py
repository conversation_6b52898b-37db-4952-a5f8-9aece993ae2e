#!/usr/bin/env python3
"""
Patch pre voice_commands.py - pridáva weather funkcionalitu

Tento súbor môže byť importovaný pre pridanie weather funkcionalite.
"""

import logging
from weather_integration import process_weather_command

logger = logging.getLogger(__name__)

def patch_voice_command_listener():
    """Patch pre voice_command_listener funkciu."""
    
    def enhanced_voice_command_listener(cursor, queue):
        """Vylepšený voice command listener s weather podporou."""
        import speech_recognition as sr
        
        recognizer = sr.Recognizer()
        microphone = sr.Microphone()
        
        logger.info("Voice command listener spustený s weather podporou.")
        
        with microphone as source:
            recognizer.adjust_for_ambient_noise(source)
        
        while cursor.running:
            if not cursor.mic_enabled:
                continue
            
            try:
                with microphone as source:
                    audio = recognizer.listen(source, timeout=1, phrase_time_limit=5)
                
                try:
                    command = recognizer.recognize_google(audio, language="sk-SK").lower()
                    logger.info(f"Rozpoznaný príkaz: '{command}'")
                    
                    # 1. Weather príkazy (najvyššia priorita)
                    weather_handled, weather_response = process_weather_command(command)
                    if weather_handled:
                        queue.put(("tts", weather_response))
                        continue
                    
                    # 2. Základné príkazy kurzora
                    handled = False
                    for cmd_key, cmd_list in cursor.commands.items():
                        if any(cmd in command for cmd in cmd_list):
                            if cmd_key in cursor.command_map:
                                func, response = cursor.command_map[cmd_key]
                                func()
                                queue.put(("tts", response))
                                handled = True
                                break
                    
                    if handled:
                        continue
                    
                    # 3. Gemini API ako fallback
                    if cursor.gemini:
                        try:
                            is_question = any(word in command for word in ["aké", "ako", "kedy", "kde", "prečo", "čo", "koľko"])
                            logger.info(f"Processing command: '{command}' (is_question: {is_question})")
                            
                            if is_question:
                                response = cursor.gemini.ask_question(command)
                                queue.put(("tts", response))
                            else:
                                response = cursor.gemini.process_command(command)
                                queue.put(("tts", response))
                        except Exception as e:
                            logger.error(f"Chyba pri volaní Gemini: {e}")
                            queue.put(("tts", "Chyba pri spracovaní príkazu."))
                    else:
                        queue.put(("tts", "Príkaz nebol rozpoznaný."))
                
                except sr.UnknownValueError:
                    logger.warning("Nepodarilo sa rozpoznať reč")
                except sr.RequestError as e:
                    logger.error(f"Chyba pri rozpoznávaní reči: {e}")
            
            except sr.WaitTimeoutError:
                pass
            except Exception as e:
                logger.error(f"Neočakávaná chyba v voice command listener: {e}")
    
    return enhanced_voice_command_listener

def apply_weather_patch():
    """Aplikuje weather patch na voice_commands modul."""
    try:
        import voice_commands
        
        # Nahradenie pôvodnej funkcie
        enhanced_listener = patch_voice_command_listener()
        voice_commands.voice_command_listener = enhanced_listener
        
        logger.info("Weather patch úspešne aplikovaný na voice_commands.")
        return True
        
    except Exception as e:
        logger.error(f"Chyba pri aplikovaní weather patch: {e}")
        return False

if __name__ == "__main__":
    # Test patch
    print("🧪 Test weather patch")
    print("=" * 30)
    
    success = apply_weather_patch()
    if success:
        print("✅ Weather patch úspešne aplikovaný")
        
        # Test weather funkcionalite
        test_commands = [
            "aké je počasie",
            "teplota vonku",
            "aké bude počasie"
        ]
        
        for command in test_commands:
            handled, response = process_weather_command(command)
            print(f"✅ '{command}' -> {response}")
    else:
        print("❌ Weather patch zlyhal")
