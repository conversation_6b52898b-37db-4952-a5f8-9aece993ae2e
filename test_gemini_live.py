#!/usr/bin/env python3
"""
Live test pre Gemini integráciu

Testuje skutočnú funkcionalitu s API kľúčom (ak je nastavený).
"""

import os
import sys
from pathlib import Path

# Pridanie project root do Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_gemini_integration():
    """Test skutočnej Gemini integrácie."""
    print("🧪 Live test Gemini integrácie")
    print("=" * 40)
    
    # Kontrola API kľúča
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY nie je nastavený")
        print("💡 Nastavte API kľúč pre live testovanie:")
        print("   set GEMINI_API_KEY=your_api_key  # Windows")
        print("   export GEMINI_API_KEY=your_api_key  # Linux/Mac")
        return False
    
    print(f"✅ API kľúč je nastavený (dĺžka: {len(api_key)} znakov)")
    
    try:
        # Import a inicializácia
        from deepseek_integration import GeminiAPI
        
        print("📋 Inicializujem GeminiAPI...")
        gemini = GeminiAPI()
        
        print(f"✅ Model: {gemini.model.model_name}")
        print(f"✅ Context memory: {gemini.context_memory}")
        print(f"✅ Max response length: {gemini.max_response_length}")
        
        # Test otázok
        print("\n🤔 Testovanie otázok:")
        test_questions = [
            "Čo je Python?",
            "Ako funguje AI?",
            "Definuj machine learning"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}. Otázka: {question}")
            try:
                response = gemini.process_command(question)
                if response["status"] == "success":
                    print(f"   ✅ Odpoveď: {response['message'][:100]}...")
                else:
                    print(f"   ❌ Chyba: {response['message']}")
            except Exception as e:
                print(f"   ❌ Výnimka: {e}")
        
        # Test akčných príkazov
        print("\n🎮 Testovanie akčných príkazov:")
        test_commands = [
            "klikni",
            "hlasitosť na 50",
            "otvor google"
        ]
        
        for i, command in enumerate(test_commands, 1):
            print(f"\n{i}. Príkaz: {command}")
            try:
                response = gemini.process_command(command)
                if response["status"] == "success":
                    if isinstance(response["message"], dict):
                        intent = response["message"].get("intent", "N/A")
                        action = response["message"].get("action_command", {}).get("name", "N/A")
                        print(f"   ✅ Intent: {intent}")
                        print(f"   ✅ Action: {action}")
                    else:
                        print(f"   ✅ Odpoveď: {response['message']}")
                else:
                    print(f"   ❌ Chyba: {response['message']}")
            except Exception as e:
                print(f"   ❌ Výnimka: {e}")
        
        # Test histórie konverzácie
        print("\n📚 História konverzácie:")
        summary = gemini.get_conversation_summary()
        for key, value in summary.items():
            print(f"   {key}: {value}")
        
        print("\n🎉 Live test dokončený úspešne!")
        return True
        
    except Exception as e:
        print(f"❌ Chyba pri live teste: {e}")
        return False

def test_without_api_key():
    """Test bez API kľúča - kontrola error handling."""
    print("\n🔒 Test bez API kľúča:")
    
    # Dočasne odstránime API kľúč
    original_key = os.environ.get("GEMINI_API_KEY")
    if "GEMINI_API_KEY" in os.environ:
        del os.environ["GEMINI_API_KEY"]
    
    try:
        from deepseek_integration import GeminiAPI
        gemini = GeminiAPI()
        print("❌ Malo by vyhodiť chybu!")
        return False
    except ValueError as e:
        print(f"✅ Správne vyhodená chyba: {e}")
        return True
    except Exception as e:
        print(f"❌ Neočakávaná chyba: {e}")
        return False
    finally:
        # Obnovenie API kľúča
        if original_key:
            os.environ["GEMINI_API_KEY"] = original_key

def main():
    """Hlavná funkcia live testu."""
    print("🚀 AirCursor Assistant - Live Test Gemini Integration")
    print("=" * 60)
    
    # Test bez API kľúča
    error_handling_ok = test_without_api_key()
    
    # Test s API kľúčom
    if os.environ.get("GEMINI_API_KEY"):
        integration_ok = test_gemini_integration()
    else:
        print("\n⚠️  Preskakujem live test - GEMINI_API_KEY nie je nastavený")
        integration_ok = True  # Nie je chyba ak nie je nastavený
    
    print("\n" + "=" * 60)
    print("📊 Súhrn testov:")
    print(f"   Error handling: {'✅ PASSED' if error_handling_ok else '❌ FAILED'}")
    
    if os.environ.get("GEMINI_API_KEY"):
        print(f"   Live integration: {'✅ PASSED' if integration_ok else '❌ FAILED'}")
        success = error_handling_ok and integration_ok
    else:
        print("   Live integration: ⚠️  SKIPPED (no API key)")
        success = error_handling_ok
    
    if success:
        print("\n🎉 Všetky testy prešli úspešne!")
        print("✅ Gemini integrácia je pripravená na používanie")
    else:
        print("\n❌ Niektoré testy zlyhali")
        print("🔧 Skontrolujte konfiguráciu a API kľúč")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
