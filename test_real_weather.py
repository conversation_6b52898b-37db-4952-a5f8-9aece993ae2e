#!/usr/bin/env python3
"""
Test real weather API funkcionalite
"""

import os
import sys
import requests
import json
from datetime import datetime

def test_openweather_api():
    """Test OpenWeatherMap API."""
    print("🌤️ Test OpenWeatherMap API")
    print("=" * 40)
    
    # Získanie API kľúča
    api_key = os.getenv('OPENWEATHER_API_KEY')
    
    if not api_key:
        print("❌ OPENWEATHER_API_KEY nie je nastavený")
        print("💡 Nastavte API kľúč:")
        print("   Windows: set OPENWEATHER_API_KEY=váš_api_kľúč")
        print("   Linux/Mac: export OPENWEATHER_API_KEY=váš_api_kľúč")
        print("📖 Návod: Pozrite WEATHER_API_SETUP.md")
        return False
    
    print(f"✅ API kľúč nájdený: {api_key[:8]}...")
    
    # Test aktuálneho počasia
    try:
        url = "http://api.openweathermap.org/data/2.5/weather"
        params = {
            'q': 'Bratislava',
            'appid': api_key,
            'units': 'metric',
            'lang': 'sk'
        }
        
        print("🔄 Testujem API volanie...")
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            # Formátovanie výsledku
            city = data['name']
            temp = data['main']['temp']
            feels_like = data['main']['feels_like']
            humidity = data['main']['humidity']
            description = data['weather'][0]['description']
            
            print("✅ API volanie úspešné!")
            print(f"📍 Mesto: {city}")
            print(f"🌡️ Teplota: {temp}°C (pocitovo {feels_like}°C)")
            print(f"💧 Vlhkosť: {humidity}%")
            print(f"☁️ Popis: {description}")
            
            return True
            
        elif response.status_code == 401:
            print("❌ Neplatný API kľúč")
            print("💡 Skontrolujte API kľúč na https://openweathermap.org/api_keys")
            return False
            
        else:
            print(f"❌ API chyba: {response.status_code}")
            print(f"📄 Odpoveď: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Timeout - API server neodpovedá")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Chyba pripojenia - skontrolujte internet")
        return False
    except Exception as e:
        print(f"❌ Neočakávaná chyba: {e}")
        return False

def test_weather_commands():
    """Test weather príkazov."""
    print("\n🎤 Test weather príkazov")
    print("=" * 40)
    
    # Simulácia weather funkcionalite
    def get_weather_response(command):
        api_key = os.getenv('OPENWEATHER_API_KEY')
        
        if not api_key:
            return "Demo: V Bratislave je 20°C, jasno. (Nastavte API kľúč pre real dáta)"
        
        try:
            url = "http://api.openweathermap.org/data/2.5/weather"
            params = {
                'q': 'Bratislava',
                'appid': api_key,
                'units': 'metric',
                'lang': 'sk'
            }
            
            response = requests.get(url, params=params, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                temp = data['main']['temp']
                feels_like = data['main']['feels_like']
                humidity = data['main']['humidity']
                description = data['weather'][0]['description']
                
                return f"V Bratislave je {temp}°C, pocitovo {feels_like}°C. {description}. Vlhkosť {humidity}%."
            else:
                return "Chyba pri získavaní weather dát"
                
        except Exception:
            return "Demo: V Bratislave je 20°C, jasno. (API nedostupné)"
    
    # Test príkazov
    test_commands = [
        "aké je počasie",
        "teplota vonku",
        "predpoveď počasia"
    ]
    
    for command in test_commands:
        response = get_weather_response(command)
        print(f"🎤 '{command}' → {response}")
    
    return True

def create_api_key_setup():
    """Vytvorí setup script pre API kľúč."""
    print("\n🔧 Vytváram setup script...")
    
    setup_script = '''@echo off
echo 🌤️ OpenWeatherMap API Setup
echo ================================

echo.
echo 1. Choďte na: https://openweathermap.org/api
echo 2. Kliknite na "Sign Up"
echo 3. Zaregistrujte sa (zadarmo)
echo 4. Choďte na "My API keys"
echo 5. Skopírujte váš API kľúč

echo.
set /p api_key="Vložte váš API kľúč: "

echo.
echo Nastavujem API kľúč...
setx OPENWEATHER_API_KEY "%api_key%"

echo.
echo ✅ API kľúč nastavený!
echo 🔄 Reštartujte terminál a spustite: python test_real_weather.py

pause
'''
    
    try:
        with open("setup_weather_api.bat", "w", encoding="utf-8") as f:
            f.write(setup_script)
        print("✅ Setup script vytvorený: setup_weather_api.bat")
        return True
    except Exception as e:
        print(f"❌ Chyba pri vytváraní setup script: {e}")
        return False

def main():
    """Hlavná funkcia."""
    print("🚀 Test Real Weather API")
    print("=" * 50)
    
    # Test 1: API kľúč a volanie
    api_test = test_openweather_api()
    
    # Test 2: Weather príkazy
    commands_test = test_weather_commands()
    
    # Test 3: Setup script
    setup_test = create_api_key_setup()
    
    # Súhrn
    print("\n📊 SÚHRN TESTOV")
    print("=" * 30)
    print(f"API test: {'✅ PASSED' if api_test else '❌ FAILED'}")
    print(f"Commands test: {'✅ PASSED' if commands_test else '❌ FAILED'}")
    print(f"Setup script: {'✅ PASSED' if setup_test else '❌ FAILED'}")
    
    if api_test:
        print("\n🎉 REAL WEATHER API FUNGUJE!")
        print("💡 Môžete používať skutočné weather dáta")
        print("🎤 Hlasové príkazy budú vracať aktuálne informácie")
    else:
        print("\n⚠️ API NEFUNGUJE")
        print("💡 Riešenia:")
        print("   1. Spustite: setup_weather_api.bat")
        print("   2. Alebo nastavte API kľúč manuálne")
        print("   3. Pozrite WEATHER_API_SETUP.md")
        print("   4. Demo režim stále funguje")
    
    print(f"\n📅 Test dokončený: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return api_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
