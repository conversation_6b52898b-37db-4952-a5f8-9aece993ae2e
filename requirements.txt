# Python version requirement
# Requires Python 3.12+

# Core dependencies
opencv-python>=4.8.0
mediapipe>=0.10.0
pyautogui>=0.9.54
SpeechRecognition>=3.10.0
gtts>=2.3.0
google-generativeai>=0.3.0

# GUI and system
Pillow>=10.0.0
screeninfo>=0.8.1

# Audio
playsound3>=1.3.0

# Windows audio control (Windows only)
pycaw>=20230407; sys_platform == "win32"
comtypes>=1.1.14; sys_platform == "win32"

# Web automation (optional)
selenium>=4.15.0
webdriver-manager>=4.0.0

# OCR (optional)
pytesseract>=0.3.10

# Development dependencies (optional)
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-xdist>=3.3.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
bandit>=1.7.5
safety>=2.3.0
psutil>=5.9.0

# Logging and utilities
colorlog>=6.7.0
